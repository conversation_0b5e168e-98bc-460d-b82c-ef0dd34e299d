<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export/ExportService.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item active">ExportService.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="89.61" aria-valuemin="0" aria-valuemax="100" style="width: 89.61%">
           <span class="sr-only">89.61% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">89.61%</div></td>
       <td class="success small"><div align="right">69&nbsp;/&nbsp;77</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="76.47" aria-valuemin="0" aria-valuemax="100" style="width: 76.47%">
           <span class="sr-only">76.47% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">76.47%</div></td>
       <td class="success small"><div align="right">13&nbsp;/&nbsp;17</div></td>
       <td class="success small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="Nzoom\Export\ExportService">ExportService</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="89.61" aria-valuemin="0" aria-valuemax="100" style="width: 89.61%">
           <span class="sr-only">89.61% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">89.61%</div></td>
       <td class="success small"><div align="right">69&nbsp;/&nbsp;77</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="76.47" aria-valuemin="0" aria-valuemax="100" style="width: 76.47%">
           <span class="sr-only">76.47% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">76.47%</div></td>
       <td class="success small"><div align="right">13&nbsp;/&nbsp;17</div></td>
       <td class="success small">31.01</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#69"><abbr title="__construct(Registry $registry, string $module, string $controller, string $type)">__construct</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">5&nbsp;/&nbsp;5</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#84"><abbr title="setModelName(string $modelName)">setModelName</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#96"><abbr title="setModelFactoryName(string $modelFactoryName)">setModelFactoryName</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#110"><abbr title="createExportAction(string $module_check, array $types, array $typeSections): ?array">createExportAction</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">9&nbsp;/&nbsp;9</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#127"><abbr title="createExportData(Outlook $outlook, array $filters, string $modelClass, $pageSize): Nzoom\Export\Entity\ExportData">createExportData</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#133"><abbr title="createExportDataWithTables(Outlook $outlook, array $filters, string $modelClass, $pageSize): Nzoom\Export\Entity\ExportData">createExportDataWithTables</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;3</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#149"><abbr title="createGeneratorFileStreamer(callable $generatorFunction, string $filename, string $mimeType, ?int $totalSize): Nzoom\Export\Streamer\GeneratorFileStreamer">createGeneratorFileStreamer</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#163"><abbr title="export(string $filename, Nzoom\Export\Entity\ExportData $data, array $options): void">export</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="75.00" aria-valuemin="0" aria-valuemax="100" style="width: 75.00%">
           <span class="sr-only">75.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">75.00%</div></td>
       <td class="success small"><div align="right">6&nbsp;/&nbsp;8</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">3.14</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#188"><abbr title="createTempStream()">createTempStream</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="75.00" aria-valuemin="0" aria-valuemax="100" style="width: 75.00%">
           <span class="sr-only">75.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">75.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;4</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2.06</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#208"><abbr title="streamToBrowser($stream, string $downloadFilename, string $mimeType): void">streamToBrowser</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">10&nbsp;/&nbsp;10</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">3</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#239"><abbr title="getFormatFactory(): Nzoom\Export\Factory\ExportFormatFactory">getFormatFactory</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">2</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#254"><abbr title="setFormatFactory(Nzoom\Export\Factory\ExportFormatFactory $formatFactory): self">setFormatFactory</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#266"><abbr title="getAdapter(): Nzoom\Export\Adapter\ExportFormatAdapterInterface">getAdapter</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">2</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#280"><abbr title="getSupportedFormats(): array">getSupportedFormats</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#291"><abbr title="isFormatSupported(string $format): bool">isFormatSupported</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#303"><abbr title="getExportFilename($prefix, string $extension)">getExportFilename</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">8&nbsp;/&nbsp;8</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">4</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#333"><abbr title="handleExportError($message, $statusCode)">handleExportError</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="84.62" aria-valuemin="0" aria-valuemax="100" style="width: 84.62%">
           <span class="sr-only">84.62% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">84.62%</div></td>
       <td class="success small"><div align="right">11&nbsp;/&nbsp;13</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">4.06</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">Export</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">Export</span><span class="default">\</span><span class="default">Adapter</span><span class="default">\</span><span class="default">ExportFormatAdapterInterface</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">Export</span><span class="default">\</span><span class="default">Entity</span><span class="default">\</span><span class="default">ExportData</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">Export</span><span class="default">\</span><span class="default">Factory</span><span class="default">\</span><span class="default">ExportFormatFactory</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">I18n</span><span class="default">\</span><span class="default">I18nAwareTrait</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;Service&nbsp;for&nbsp;handling&nbsp;grid&nbsp;exports</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">ExportService</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">I18nAwareTrait</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;\Registry</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$registry</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$module</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$controller</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$modelName</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$modelFactoryName</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$systemExportModules</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;ExportFormatFactory</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">ExportFormatFactory</span><span class="default">&nbsp;</span><span class="default">$formatFactory</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$type</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;ExportFormatAdapterInterface</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="default">ExportFormatAdapterInterface</span><span class="default">&nbsp;</span><span class="default">$adapter</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;ExportService&nbsp;constructor.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;\Registry&nbsp;$registry</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$module</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$controller</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$type&nbsp;The&nbsp;export&nbsp;type/format&nbsp;(e.g.,&nbsp;'csv',&nbsp;'excel',&nbsp;'pdf')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">__construct</span><span class="keyword">(</span><span class="default">\</span><span class="default">Registry</span><span class="default">&nbsp;</span><span class="default">$registry</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$module</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$controller</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$type</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="26 tests cover line 71" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testConstructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithInvalidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateTempStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateGeneratorFileStreamer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testIsFormatSupported&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetSupportedFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelFactoryName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$registry</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="26 tests cover line 72" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testConstructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithInvalidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateTempStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateGeneratorFileStreamer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testIsFormatSupported&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetSupportedFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelFactoryName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">module</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$module</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="26 tests cover line 73" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testConstructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithInvalidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateTempStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateGeneratorFileStreamer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testIsFormatSupported&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetSupportedFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelFactoryName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">controller</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$controller</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="26 tests cover line 74" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testConstructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithInvalidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateTempStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateGeneratorFileStreamer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testIsFormatSupported&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetSupportedFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelFactoryName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">type</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$type</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="26 tests cover line 75" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testConstructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithInvalidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateTempStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateGeneratorFileStreamer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testIsFormatSupported&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetSupportedFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelFactoryName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">setTranslator</span><span class="keyword">(</span><span class="default">$registry</span><span class="keyword">[</span><span class="default">'translater'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;the&nbsp;model&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$modelName</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;$this</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setModelName</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$modelName</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 86" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">modelName</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$modelName</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 87" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;the&nbsp;model&nbsp;factory&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$modelFactoryName</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;$this</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setModelFactoryName</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$modelFactoryName</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 98" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelFactoryName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">modelFactoryName</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$modelFactoryName</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 99" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetModelFactoryName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Create&nbsp;export&nbsp;action</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$module_check</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;array&nbsp;$types</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;array&nbsp;$typeSections</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array|null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">createExportAction</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$module_check</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$types</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$typeSections</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Create&nbsp;and&nbsp;configure&nbsp;the&nbsp;factory</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 113" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$factory</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">ExportActionFactory</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 114" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 115" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">module</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 116" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">controller</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 117" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">modelName</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 118" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">modelFactoryName</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 119" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="keyword">[</span><span class="default">'translater'</span><span class="keyword">]</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 120" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Use&nbsp;the&nbsp;factory&nbsp;to&nbsp;create&nbsp;the&nbsp;export&nbsp;action</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 123" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportAction&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$factory</span><span class="keyword">(</span><span class="default">$module_check</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$types</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$typeSections</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">createExportData</span><span class="keyword">(</span><span class="default">\</span><span class="default">Outlook</span><span class="default">&nbsp;</span><span class="default">$outlook</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$filters</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$modelClass</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$pageSize</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">200</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">ExportData</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 129" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportData&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$dataFactory</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">DataFactory</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 130" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateExportData&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$dataFactory</span><span class="default">-&gt;</span><span class="default">createStreaming</span><span class="keyword">(</span><span class="default">$modelClass</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$filters</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$outlook</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$pageSize</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">createExportDataWithTables</span><span class="keyword">(</span><span class="default">\</span><span class="default">Outlook</span><span class="default">&nbsp;</span><span class="default">$outlook</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$filters</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$modelClass</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$pageSize</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">200</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">ExportData</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$dataFactory</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">DataFactory</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$dataFactory</span><span class="default">-&gt;</span><span class="default">withModelTableProvider</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$dataFactory</span><span class="default">-&gt;</span><span class="default">createStreaming</span><span class="keyword">(</span><span class="default">$modelClass</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$filters</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$outlook</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$pageSize</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Create&nbsp;a&nbsp;GeneratorFileStreamer&nbsp;instance</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;callable&nbsp;$generatorFunction&nbsp;Function&nbsp;that&nbsp;returns&nbsp;a&nbsp;generator</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$filename&nbsp;The&nbsp;filename&nbsp;to&nbsp;present&nbsp;to&nbsp;the&nbsp;browser</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$mimeType&nbsp;The&nbsp;MIME&nbsp;type&nbsp;for&nbsp;the&nbsp;content</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int|null&nbsp;$totalSize&nbsp;Total&nbsp;size&nbsp;in&nbsp;bytes&nbsp;if&nbsp;known</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;\Nzoom\Export\Streamer\GeneratorFileStreamer</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">createGeneratorFileStreamer</span><span class="keyword">(</span><span class="keyword">callable</span><span class="default">&nbsp;</span><span class="default">$generatorFunction</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$mimeType</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">'application/octet-stream'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">int</span><span class="default">&nbsp;</span><span class="default">$totalSize</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">Export</span><span class="default">\</span><span class="default">Streamer</span><span class="default">\</span><span class="default">GeneratorFileStreamer</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 151" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateGeneratorFileStreamer&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">Export</span><span class="default">\</span><span class="default">Streamer</span><span class="default">\</span><span class="default">GeneratorFileStreamer</span><span class="keyword">(</span><span class="default">$generatorFunction</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$mimeType</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$totalSize</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Export&nbsp;data&nbsp;using&nbsp;the&nbsp;configured&nbsp;type</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$filename&nbsp;The&nbsp;filename&nbsp;to&nbsp;present&nbsp;to&nbsp;the&nbsp;browser</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;ExportData&nbsp;$data&nbsp;The&nbsp;export&nbsp;data&nbsp;to&nbsp;process</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;array&nbsp;$options&nbsp;Export&nbsp;options&nbsp;(e.g.,&nbsp;sizing&nbsp;constraints)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\Exception</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">export</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">ExportData</span><span class="default">&nbsp;</span><span class="default">$data</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$options</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Use&nbsp;adapter&nbsp;pattern&nbsp;with&nbsp;temp&nbsp;stream&nbsp;+&nbsp;streaming&nbsp;approach</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Generate&nbsp;export&nbsp;to&nbsp;temporary&nbsp;stream</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 168" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$tempStream</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">createTempStream</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 170" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$adapter</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getAdapter</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 171" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$adapter</span><span class="default">-&gt;</span><span class="default">export</span><span class="keyword">(</span><span class="default">$tempStream</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">type</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$data</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$options</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Stream&nbsp;to&nbsp;browser</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 174" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">streamToBrowser</span><span class="keyword">(</span><span class="default">$tempStream</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$adapter</span><span class="default">-&gt;</span><span class="default">getMimeType</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">type</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 175" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">\</span><span class="default">InvalidArgumentException</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 176" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handleExportError</span><span class="keyword">(</span><span class="default">'Unsupported&nbsp;export&nbsp;format:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">type</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">\</span><span class="default">Exception</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handleExportError</span><span class="keyword">(</span><span class="default">'Export&nbsp;failed:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Create&nbsp;a&nbsp;temporary&nbsp;stream&nbsp;for&nbsp;export&nbsp;generation</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;resource&nbsp;A&nbsp;writable&nbsp;temporary&nbsp;stream</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\Exception&nbsp;If&nbsp;temp&nbsp;stream&nbsp;cannot&nbsp;be&nbsp;created</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">createTempStream</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 190" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateTempStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$tempStream</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">fopen</span><span class="keyword">(</span><span class="default">'php://temp'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'w+'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 192" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateTempStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$tempStream</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">false</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Exception</span><span class="keyword">(</span><span class="default">'Failed&nbsp;to&nbsp;create&nbsp;temporary&nbsp;stream&nbsp;for&nbsp;export'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 196" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testCreateTempStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$tempStream</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Stream&nbsp;data&nbsp;to&nbsp;the&nbsp;browser&nbsp;using&nbsp;PointerFileStreamer</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;resource&nbsp;$stream&nbsp;The&nbsp;stream&nbsp;containing&nbsp;the&nbsp;export&nbsp;data</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$downloadFilename&nbsp;Filename&nbsp;to&nbsp;present&nbsp;to&nbsp;the&nbsp;browser</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$mimeType&nbsp;MIME&nbsp;type&nbsp;for&nbsp;the&nbsp;file</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\Exception&nbsp;If&nbsp;stream&nbsp;cannot&nbsp;be&nbsp;sent</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">streamToBrowser</span><span class="keyword">(</span><span class="default">$stream</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$downloadFilename</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$mimeType</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 210" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithInvalidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">is_resource</span><span class="keyword">(</span><span class="default">$stream</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 211" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithInvalidStream&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Exception</span><span class="keyword">(</span><span class="default">'Invalid&nbsp;stream&nbsp;provided&nbsp;for&nbsp;streaming'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Create&nbsp;and&nbsp;use&nbsp;PointerFileStreamer</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 216" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$streamer</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">Export</span><span class="default">\</span><span class="default">Streamer</span><span class="default">\</span><span class="default">PointerFileStreamer</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 217" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$stream</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 218" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$downloadFilename</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 219" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$mimeType</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 220" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Stream&nbsp;the&nbsp;data&nbsp;(this&nbsp;will&nbsp;handle&nbsp;cleanup&nbsp;and&nbsp;exit)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 223" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$streamer</span><span class="default">-&gt;</span><span class="default">stream</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">finally</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Clean&nbsp;up&nbsp;stream</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 227" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">is_resource</span><span class="keyword">(</span><span class="default">$stream</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 228" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testStreamToBrowserWithValidStream&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">fclose</span><span class="keyword">(</span><span class="default">$stream</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="230" href="#230">230</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="231" href="#231">231</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="232" href="#232">232</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="233" href="#233">233</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="234" href="#234">234</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;the&nbsp;export&nbsp;format&nbsp;factory</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="235" href="#235">235</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Creates&nbsp;the&nbsp;factory&nbsp;automatically&nbsp;if&nbsp;it&nbsp;doesn't&nbsp;exist</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="236" href="#236">236</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="237" href="#237">237</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;ExportFormatFactory</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="238" href="#238">238</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="239" href="#239">239</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getFormatFactory</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">ExportFormatFactory</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="240" href="#240">240</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 241" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testIsFormatSupported&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetSupportedFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="241" href="#241">241</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">formatFactory</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 242" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="242" href="#242">242</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">formatFactory</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">ExportFormatFactory</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">module</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">controller</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="243" href="#243">243</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="244" href="#244">244</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 245" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testIsFormatSupported&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetSupportedFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="245" href="#245">245</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">formatFactory</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="246" href="#246">246</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="247" href="#247">247</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="248" href="#248">248</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="249" href="#249">249</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;the&nbsp;export&nbsp;format&nbsp;factory</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="250" href="#250">250</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="251" href="#251">251</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;ExportFormatFactory&nbsp;$formatFactory</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="252" href="#252">252</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;$this</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="253" href="#253">253</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="254" href="#254">254</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setFormatFactory</span><span class="keyword">(</span><span class="default">ExportFormatFactory</span><span class="default">&nbsp;</span><span class="default">$formatFactory</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">self</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="255" href="#255">255</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 256" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testIsFormatSupported&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetSupportedFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="256" href="#256">256</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">formatFactory</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$formatFactory</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 257" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testIsFormatSupported&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetSupportedFormats&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testSetFormatFactory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="257" href="#257">257</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="258" href="#258">258</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="259" href="#259">259</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="260" href="#260">260</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="261" href="#261">261</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;the&nbsp;export&nbsp;adapter&nbsp;for&nbsp;the&nbsp;configured&nbsp;type</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="262" href="#262">262</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Creates&nbsp;the&nbsp;adapter&nbsp;automatically&nbsp;if&nbsp;it&nbsp;doesn't&nbsp;exist</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="263" href="#263">263</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="264" href="#264">264</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;ExportFormatAdapterInterface</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="265" href="#265">265</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="266" href="#266">266</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getAdapter</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">ExportFormatAdapterInterface</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="267" href="#267">267</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 268" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="268" href="#268">268</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">adapter</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 269" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="269" href="#269">269</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">adapter</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getFormatFactory</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">createAdapter</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">type</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'extension'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">type</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="270" href="#270">270</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="271" href="#271">271</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 272" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetAdapter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testLazyInitialization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="272" href="#272">272</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">adapter</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="273" href="#273">273</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="274" href="#274">274</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="275" href="#275">275</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="276" href="#276">276</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;supported&nbsp;export&nbsp;formats</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="277" href="#277">277</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="278" href="#278">278</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="279" href="#279">279</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="280" href="#280">280</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getSupportedFormats</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="281" href="#281">281</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 282" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetSupportedFormats&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="282" href="#282">282</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getFormatFactory</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">getSupportedFormats</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="283" href="#283">283</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="284" href="#284">284</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="285" href="#285">285</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="286" href="#286">286</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Check&nbsp;if&nbsp;a&nbsp;format&nbsp;is&nbsp;supported</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="287" href="#287">287</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="288" href="#288">288</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$format</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="289" href="#289">289</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="290" href="#290">290</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="291" href="#291">291</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">isFormatSupported</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$format</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="292" href="#292">292</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 293" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testIsFormatSupported&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="293" href="#293">293</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getFormatFactory</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">isFormatSupported</span><span class="keyword">(</span><span class="default">$format</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="294" href="#294">294</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="295" href="#295">295</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="296" href="#296">296</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="297" href="#297">297</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;export&nbsp;filename</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="298" href="#298">298</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="299" href="#299">299</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string|array|null&nbsp;$prefix&nbsp;Filename&nbsp;prefix</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="300" href="#300">300</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$extension&nbsp;File&nbsp;extension&nbsp;without&nbsp;dot</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="301" href="#301">301</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;Filename</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="302" href="#302">302</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="303" href="#303">303</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getExportFilename</span><span class="keyword">(</span><span class="default">$prefix</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$extension</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="304" href="#304">304</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="305" href="#305">305</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Get&nbsp;filename&nbsp;from&nbsp;request&nbsp;or&nbsp;generate&nbsp;one</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 306" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="306" href="#306">306</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$filename</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$prefix</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="307" href="#307">307</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="308" href="#308">308</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Handle&nbsp;array&nbsp;or&nbsp;null&nbsp;cases</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 309" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="309" href="#309">309</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">is_array</span><span class="keyword">(</span><span class="default">$filename</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 310" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="310" href="#310">310</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$filename</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">[</span><span class="default">0</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="311" href="#311">311</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="312" href="#312">312</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 313" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="313" href="#313">313</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$filename</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 314" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="314" href="#314">314</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$filename</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">strtolower</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">module</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'_'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">controller</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'_export_'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">date</span><span class="keyword">(</span><span class="default">'Y-m-d_H-i-s'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="315" href="#315">315</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="316" href="#316">316</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="317" href="#317">317</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Add&nbsp;extension&nbsp;if&nbsp;not&nbsp;present</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 318" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="318" href="#318">318</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">substr</span><span class="keyword">(</span><span class="default">$filename</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">-</span><span class="keyword">(</span><span class="default">strlen</span><span class="keyword">(</span><span class="default">$extension</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">+</span><span class="default">&nbsp;</span><span class="default">1</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">'.'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$extension</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 319" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="319" href="#319">319</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$filename</span><span class="default">&nbsp;</span><span class="default">.=</span><span class="default">&nbsp;</span><span class="default">'.'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$extension</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="320" href="#320">320</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="321" href="#321">321</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 322" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithExistingExtension&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testGetExportFilenameWithString&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="322" href="#322">322</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$filename</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="323" href="#323">323</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="324" href="#324">324</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="325" href="#325">325</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="326" href="#326">326</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Handle&nbsp;export&nbsp;error</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="327" href="#327">327</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="328" href="#328">328</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$message&nbsp;Error&nbsp;message</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="329" href="#329">329</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int&nbsp;$statusCode&nbsp;HTTP&nbsp;status&nbsp;code&nbsp;to&nbsp;use&nbsp;(default:&nbsp;400)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="330" href="#330">330</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="331" href="#331">331</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\Exception</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="332" href="#332">332</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="333" href="#333">333</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handleExportError</span><span class="keyword">(</span><span class="default">$message</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$statusCode</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">400</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="334" href="#334">334</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="335" href="#335">335</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Log&nbsp;the&nbsp;error&nbsp;if&nbsp;logger&nbsp;is&nbsp;available</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 336" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="336" href="#336">336</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="keyword">[</span><span class="default">'logger'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 337" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="337" href="#337">337</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="keyword">[</span><span class="default">'logger'</span><span class="keyword">]</span><span class="default">-&gt;</span><span class="default">error</span><span class="keyword">(</span><span class="default">'Export&nbsp;error:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$message</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="338" href="#338">338</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="339" href="#339">339</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="340" href="#340">340</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Set&nbsp;error&nbsp;message&nbsp;in&nbsp;registry&nbsp;for&nbsp;AJAX&nbsp;response</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 341" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="341" href="#341">341</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="default">-&gt;</span><span class="default">set</span><span class="keyword">(</span><span class="default">'ajax_result'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 342" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="342" href="#342">342</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'error'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$message</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 343" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="343" href="#343">343</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'status'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'error'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 344" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="344" href="#344">344</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'timestamp'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">date</span><span class="keyword">(</span><span class="default">'Y-m-d&nbsp;H:i:s'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 345" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="345" href="#345">345</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="346" href="#346">346</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="347" href="#347">347</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;If&nbsp;this&nbsp;is&nbsp;an&nbsp;AJAX&nbsp;request,&nbsp;send&nbsp;appropriate&nbsp;headers</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 348" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="348" href="#348">348</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$_SERVER</span><span class="keyword">[</span><span class="default">'HTTP_X_REQUESTED_WITH'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 349" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithoutLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithLogger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="349" href="#349">349</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">strtolower</span><span class="keyword">(</span><span class="default">$_SERVER</span><span class="keyword">[</span><span class="default">'HTTP_X_REQUESTED_WITH'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">'xmlhttprequest'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="350" href="#350">350</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 351" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="351" href="#351">351</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">http_response_code</span><span class="keyword">(</span><span class="default">$statusCode</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 352" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testHandleExportErrorWithAjaxRequest&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="352" href="#352">352</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">header</span><span class="keyword">(</span><span class="default">'Content-Type:&nbsp;application/json'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="353" href="#353">353</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">echo</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">registry</span><span class="default">-&gt;</span><span class="default">get</span><span class="keyword">(</span><span class="default">'ajax_result'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="354" href="#354">354</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">exit</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="355" href="#355">355</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="356" href="#356">356</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="357" href="#357">357</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;For&nbsp;non-AJAX&nbsp;requests,&nbsp;we'll&nbsp;let&nbsp;the&nbsp;controller&nbsp;handle&nbsp;the&nbsp;response</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="358" href="#358">358</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="359" href="#359">359</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Mon Jun 23 16:53:56 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/popper.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/bootstrap.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/file.js?v=9.2.32" type="text/javascript"></script>
 </body>
</html>
