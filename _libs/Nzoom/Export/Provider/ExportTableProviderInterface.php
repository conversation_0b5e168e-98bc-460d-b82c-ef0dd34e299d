<?php

namespace Nzoom\Export\Provider;

use Nzoom\Export\Entity\ExportTableCollection;

/**
 * Interface ExportTableProviderInterface
 *
 * Defines the contract for providing table data from records
 * Implementations should extract related table data from source records
 */
interface ExportTableProviderInterface
{
    /**
     * Get table data for a given record
     *
     * @param mixed $record The source record (e.g., Model instance)
     * @param array $options Configuration options for table extraction
     * @return ExportTableCollection Collection of tables for this record
     */
    public function getTablesForRecord($record, array $options = []): ExportTableCollection;

    /**
     * Get table configuration for a specific table type
     * Returns information about columns, headers, etc.
     *
     * @param string $tableType
     * @return array Configuration array
     */
    public function getTableConfiguration(string $tableType): array;

    /**
     * Validate if a record can provide the requested table types
     *
     * @param mixed $record The source record
     * @param array $requestedTableTypes Array of table types to validate
     * @return bool True if all requested table types can be provided
     */
    public function validateRecord($record, array $requestedTableTypes = []): bool;
}
