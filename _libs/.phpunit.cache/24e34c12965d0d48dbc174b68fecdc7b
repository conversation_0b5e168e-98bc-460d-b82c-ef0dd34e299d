a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Provider\ModelTableProvider";a:6:{s:4:"name";s:18:"ModelTableProvider";s:14:"namespacedName";s:40:"Nzoom\Export\Provider\ModelTableProvider";s:9:"namespace";s:21:"Nzoom\Export\Provider";s:9:"startLine";i:18;s:7:"endLine";i:553;s:7:"methods";a:17:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:47:"__construct(Registry $registry, array $options)";s:10:"visibility";s:6:"public";s:9:"startLine";i:41;s:7:"endLine";i:45;s:3:"ccn";i:1;}s:17:"getDefaultOptions";a:6:{s:10:"methodName";s:17:"getDefaultOptions";s:9:"signature";s:26:"getDefaultOptions(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:52;s:7:"endLine";i:59;s:3:"ccn";i:1;}s:18:"getTablesForRecord";a:6:{s:10:"methodName";s:18:"getTablesForRecord";s:9:"signature";s:86:"getTablesForRecord($record, array $options): Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:64;s:7:"endLine";i:94;s:3:"ccn";i:8;}s:25:"discoverGroupingVariables";a:6:{s:10:"methodName";s:25:"discoverGroupingVariables";s:9:"signature";s:46:"discoverGroupingVariables(Model $model): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:102;s:7:"endLine";i:143;s:3:"ccn";i:10;}s:27:"createTableFromGroupingData";a:6:{s:10:"methodName";s:27:"createTableFromGroupingData";s:9:"signature";s:129:"createTableFromGroupingData(Model $model, string $varName, array $groupingData, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:154;s:7:"endLine";i:191;s:3:"ccn";i:4;}s:22:"getOrCreateTableHeader";a:6:{s:10:"methodName";s:22:"getOrCreateTableHeader";s:9:"signature";s:133:"getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden, array $types): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:203;s:7:"endLine";i:235;s:3:"ccn";i:6;}s:15:"formatTableName";a:6:{s:10:"methodName";s:15:"formatTableName";s:9:"signature";s:40:"formatTableName(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:243;s:7:"endLine";i:250;s:3:"ccn";i:1;}s:27:"convertFieldTypeToValueType";a:6:{s:10:"methodName";s:27:"convertFieldTypeToValueType";s:9:"signature";s:54:"convertFieldTypeToValueType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:258;s:7:"endLine";i:282;s:3:"ccn";i:16;}s:15:"guessColumnType";a:6:{s:10:"methodName";s:15:"guessColumnType";s:9:"signature";s:40:"guessColumnType(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:290;s:7:"endLine";i:317;s:3:"ccn";i:6;}s:29:"populateTableFromGroupingData";a:6:{s:10:"methodName";s:29:"populateTableFromGroupingData";s:9:"signature";s:149:"populateTableFromGroupingData(Nzoom\Export\Entity\ExportTable $table, array $values, array $names, array $hidden, array $types, array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:329;s:7:"endLine";i:337;s:3:"ccn";i:3;}s:23:"createRecordFromRowData";a:6:{s:10:"methodName";s:23:"createRecordFromRowData";s:9:"signature";s:133:"createRecordFromRowData(array $rowData, array $names, array $hidden, array $types, array $options): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:349;s:7:"endLine";i:373;s:3:"ccn";i:5;}s:11:"formatValue";a:6:{s:10:"methodName";s:11:"formatValue";s:9:"signature";s:66:"formatValue($value, string $type, ?string $format, array $options)";s:10:"visibility";s:7:"private";s:9:"startLine";i:386;s:7:"endLine";i:440;s:3:"ccn";i:19;}s:21:"getTableConfiguration";a:6:{s:10:"methodName";s:21:"getTableConfiguration";s:9:"signature";s:47:"getTableConfiguration(string $tableType): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:445;s:7:"endLine";i:452;s:3:"ccn";i:1;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:57:"validateRecord($record, array $requestedTableTypes): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:457;s:7:"endLine";i:466;s:3:"ccn";i:2;}s:15:"shouldSkipTable";a:6:{s:10:"methodName";s:15:"shouldSkipTable";s:9:"signature";s:61:"shouldSkipTable(Nzoom\Export\Entity\ExportTable $table): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:474;s:7:"endLine";i:485;s:3:"ccn";i:2;}s:15:"isTableRowEmpty";a:6:{s:10:"methodName";s:15:"isTableRowEmpty";s:9:"signature";s:63:"isTableRowEmpty(Nzoom\Export\Entity\ExportRecord $record): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:493;s:7:"endLine";i:504;s:3:"ccn";i:3;}s:18:"isValueEmptyOrZero";a:6:{s:10:"methodName";s:18:"isValueEmptyOrZero";s:9:"signature";s:32:"isValueEmptyOrZero($value): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:512;s:7:"endLine";i:552;s:3:"ccn";i:8;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:554;s:18:"commentLinesOfCode";i:166;s:21:"nonCommentLinesOfCode";i:388;}s:15:"ignoredLinesFor";a:1:{i:0;i:18;}s:17:"executableLinesIn";a:193:{i:41;i:4;i:43;i:5;i:44;i:6;i:54;i:7;i:55;i:7;i:56;i:7;i:57;i:7;i:58;i:7;i:64;i:8;i:66;i:9;i:67;i:10;i:69;i:11;i:70;i:12;i:75;i:13;i:77;i:14;i:78;i:15;i:80;i:16;i:81;i:17;i:84;i:18;i:86;i:19;i:87;i:20;i:88;i:20;i:89;i:20;i:93;i:21;i:104;i:22;i:110;i:23;i:111;i:24;i:115;i:25;i:116;i:26;i:117;i:27;i:120;i:28;i:123;i:29;i:124;i:30;i:128;i:31;i:129;i:32;i:130;i:33;i:133;i:34;i:135;i:35;i:136;i:36;i:137;i:36;i:138;i:36;i:142;i:37;i:157;i:38;i:158;i:39;i:159;i:40;i:160;i:41;i:161;i:42;i:163;i:43;i:164;i:44;i:168;i:45;i:171;i:46;i:174;i:47;i:175;i:47;i:176;i:47;i:177;i:47;i:178;i:47;i:179;i:47;i:180;i:47;i:183;i:48;i:186;i:49;i:187;i:50;i:190;i:51;i:203;i:52;i:206;i:53;i:207;i:54;i:211;i:55;i:213;i:56;i:215;i:57;i:216;i:58;i:219;i:59;i:221;i:60;i:222;i:61;i:224;i:62;i:227;i:63;i:228;i:64;i:232;i:65;i:234;i:66;i:246;i:67;i:247;i:68;i:249;i:69;i:261;i:70;i:262;i:71;i:264;i:72;i:265;i:73;i:267;i:74;i:268;i:75;i:269;i:76;i:270;i:77;i:271;i:78;i:272;i:79;i:273;i:80;i:274;i:81;i:275;i:82;i:276;i:83;i:277;i:84;i:278;i:85;i:280;i:86;i:292;i:87;i:295;i:88;i:296;i:89;i:297;i:90;i:299;i:91;i:303;i:92;i:304;i:93;i:305;i:94;i:307;i:95;i:311;i:96;i:312;i:97;i:316;i:98;i:331;i:99;i:332;i:100;i:333;i:101;i:334;i:102;i:351;i:103;i:353;i:104;i:355;i:105;i:356;i:106;i:360;i:107;i:362;i:108;i:363;i:109;i:365;i:110;i:367;i:111;i:369;i:112;i:372;i:113;i:388;i:114;i:389;i:115;i:393;i:116;i:394;i:117;i:395;i:118;i:396;i:119;i:397;i:120;i:399;i:121;i:401;i:122;i:402;i:123;i:404;i:124;i:407;i:125;i:409;i:126;i:411;i:127;i:412;i:128;i:413;i:129;i:414;i:130;i:415;i:131;i:417;i:132;i:419;i:133;i:420;i:134;i:422;i:135;i:425;i:136;i:427;i:137;i:429;i:138;i:430;i:139;i:432;i:140;i:433;i:141;i:435;i:142;i:436;i:143;i:439;i:144;i:448;i:145;i:449;i:145;i:450;i:145;i:451;i:145;i:457;i:146;i:459;i:147;i:460;i:148;i:465;i:149;i:477;i:150;i:478;i:151;i:481;i:152;i:482;i:153;i:484;i:154;i:495;i:155;i:497;i:156;i:498;i:157;i:499;i:158;i:503;i:159;i:515;i:160;i:516;i:161;i:520;i:162;i:521;i:163;i:525;i:164;i:526;i:165;i:530;i:166;i:531;i:167;i:535;i:168;i:536;i:169;i:537;i:169;i:538;i:169;i:539;i:169;i:540;i:169;i:541;i:169;i:542;i:169;i:543;i:169;i:545;i:170;i:546;i:171;i:551;i:172;}}