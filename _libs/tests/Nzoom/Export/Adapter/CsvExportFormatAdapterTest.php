<?php

namespace Tests\Nzoom\Export\Adapter;

use Tests\Nzoom\Export\ExportTestCase;
use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Adapter\CsvExportFormatAdapter;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\I18n\I18n;

/**
 * Mock logger for testing
 */
class MockCsvLogger
{
    public $errors = [];
    public $infos = [];

    public function error($message)
    {
        $this->errors[] = $message;
    }

    public function info($message)
    {
        $this->infos[] = $message;
    }

    public function warning($message)
    {
        // For compatibility
    }
}

/**
 * Test case for CsvExportFormatAdapter
 *
 * Focuses on CSV-specific functionality, avoiding duplication with AbstractExportFormatAdapterTest
 */
class CsvExportFormatAdapterTest extends ExportTestCase
{
    private CsvExportFormatAdapter $adapter;
    private RegistryMock $registry;
    private $translator;
    private MockCsvLogger $logger;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../../TestHelpers/GlobalMocks.php';

        // Create mock dependencies
        $this->translator = $this->createMock(I18n::class);
        $this->translator->method('translate')->willReturnCallback(function($key, $params = []) {
            return $key; // Simple pass-through for testing
        });

        $this->logger = new MockCsvLogger();

        // Create registry mock
        $this->registry = new RegistryMock();
        $this->registry->set('translater', $this->translator);
        $this->registry->set('logger', $this->logger);

        // Create adapter instance
        $this->adapter = new CsvExportFormatAdapter($this->registry, 'test_module', 'test_controller');
    }

    // Test CSV-specific interface methods

    public function testGetSupportedExtensions(): void
    {
        $extensions = CsvExportFormatAdapter::getSupportedExtensions();
        $this->assertIsArray($extensions);
        $this->assertContains('csv', $extensions);
        $this->assertCount(1, $extensions);
    }

    public function testSupportsFormat(): void
    {
        $this->assertTrue(CsvExportFormatAdapter::supportsFormat('csv'));
        $this->assertTrue(CsvExportFormatAdapter::supportsFormat('CSV')); // Case insensitive
        $this->assertFalse(CsvExportFormatAdapter::supportsFormat('xlsx'));
        $this->assertFalse(CsvExportFormatAdapter::supportsFormat('pdf'));
        $this->assertFalse(CsvExportFormatAdapter::supportsFormat(''));
    }

    public function testGetMimeType(): void
    {
        $this->assertEquals('text/csv', $this->adapter->getMimeType());
        $this->assertEquals('text/csv', $this->adapter->getMimeType('csv'));
        $this->assertEquals('text/csv', $this->adapter->getMimeType('unsupported'));
    }

    public function testGetDefaultExtension(): void
    {
        $this->assertEquals('csv', $this->adapter->getDefaultExtension());
    }

    public function testGetFormatName(): void
    {
        $this->assertEquals('csv', $this->adapter->getFormatName());
    }

    public function testGetFormatOptions(): void
    {
        $options = $this->adapter->getFormatOptions();

        $this->assertIsArray($options);
        $this->assertArrayHasKey('delimiter', $options);
        $this->assertArrayHasKey('enclosure', $options);
        $this->assertArrayHasKey('include_bom', $options);
        $this->assertArrayHasKey('date_format', $options);
        $this->assertArrayHasKey('datetime_format', $options);

        // Test delimiter options
        $delimiterOption = $options['delimiter'];
        $this->assertEquals('select', $delimiterOption['type']);
        $this->assertEquals('Field Delimiter', $delimiterOption['label']);
        $this->assertArrayHasKey('comma', $delimiterOption['options']);
        $this->assertArrayHasKey('semicolon', $delimiterOption['options']);
        $this->assertArrayHasKey('tab', $delimiterOption['options']);
        $this->assertArrayHasKey('pipe', $delimiterOption['options']);
        $this->assertEquals('comma', $delimiterOption['default']);

        // Test enclosure options
        $enclosureOption = $options['enclosure'];
        $this->assertEquals('select', $enclosureOption['type']);
        $this->assertEquals('Text Enclosure', $enclosureOption['label']);
        $this->assertArrayHasKey('"', $enclosureOption['options']);
        $this->assertArrayHasKey("'", $enclosureOption['options']);
        $this->assertEquals('"', $enclosureOption['default']);

        // Test BOM options
        $bomOption = $options['include_bom'];
        $this->assertEquals('checkbox', $bomOption['type']);
        $this->assertEquals('Include UTF-8 BOM', $bomOption['label']);
        $this->assertFalse($bomOption['default']);

        // Test date format options
        $dateFormatOption = $options['date_format'];
        $this->assertEquals('select', $dateFormatOption['type']);
        $this->assertEquals('Date Format', $dateFormatOption['label']);
        $this->assertArrayHasKey('Y-m-d', $dateFormatOption['options']);
        $this->assertArrayHasKey('d.m.Y', $dateFormatOption['options']);
        $this->assertArrayHasKey('d/m/Y', $dateFormatOption['options']);
        $this->assertArrayHasKey('m/d/Y', $dateFormatOption['options']);
        $this->assertEquals('Y-m-d', $dateFormatOption['default']);

        // Test datetime format options
        $datetimeFormatOption = $options['datetime_format'];
        $this->assertEquals('select', $datetimeFormatOption['type']);
        $this->assertEquals('DateTime Format', $datetimeFormatOption['label']);
        $this->assertArrayHasKey('Y-m-d H:i:s', $datetimeFormatOption['options']);
        $this->assertArrayHasKey('d.m.Y H:i', $datetimeFormatOption['options']);
        $this->assertArrayHasKey('d/m/Y H:i', $datetimeFormatOption['options']);
        $this->assertArrayHasKey('m/d/Y H:i', $datetimeFormatOption['options']);
        $this->assertEquals('Y-m-d H:i:s', $datetimeFormatOption['default']);
    }

    // Test CSV-specific export functionality

    public function testExportWithUnsupportedType(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unsupported export type: xlsx');

        $this->adapter->export($tempFile, 'xlsx', $exportData);

        // Clean up
        if (file_exists($tempFile)) {
            unlink($tempFile);
        }
    }

    public function testExportToTempFile(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        try {
            $this->adapter->export($tempFile, 'csv', $exportData);

            // Verify file was created
            $this->assertFileExists($tempFile);
            $this->assertGreaterThan(0, filesize($tempFile));

            // Verify CSV content
            $content = file_get_contents($tempFile);
            $this->assertStringContainsString('ID,Name,Email', $content);
            $this->assertStringContainsString('1,"John Doe",<EMAIL>', $content);
            $this->assertStringContainsString('2,"Jane Smith",<EMAIL>', $content);

        } finally {
            // Clean up
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportToPhpOutput(): void
    {
        $exportData = $this->createTestExportData();

        // Capture output
        ob_start();

        try {
            $this->adapter->export('php://output', 'csv', $exportData);
            $output = ob_get_contents();

            // Should have CSV content
            $this->assertNotEmpty($output);
            $this->assertStringContainsString('ID,Name,Email', $output);

        } finally {
            ob_end_clean();
        }
    }

    public function testExportWithCustomDelimiter(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        $options = ['delimiter' => 'semicolon'];

        try {
            $this->adapter->export($tempFile, 'csv', $exportData, $options);

            $content = file_get_contents($tempFile);
            $this->assertStringContainsString('ID;Name;Email', $content);
            $this->assertStringContainsString('1;"John Doe";<EMAIL>', $content);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithTabDelimiter(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        $options = ['delimiter' => 'tab'];

        try {
            $this->adapter->export($tempFile, 'csv', $exportData, $options);

            $content = file_get_contents($tempFile);
            $this->assertStringContainsString("ID\tName\tEmail", $content);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithPipeDelimiter(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        $options = ['delimiter' => 'pipe'];

        try {
            $this->adapter->export($tempFile, 'csv', $exportData, $options);

            $content = file_get_contents($tempFile);
            $this->assertStringContainsString('ID|Name|Email', $content);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithCustomEnclosure(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        $options = ['enclosure' => "'"];

        try {
            $this->adapter->export($tempFile, 'csv', $exportData, $options);

            $content = file_get_contents($tempFile);
            $this->assertStringContainsString("'John Doe'", $content);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithBom(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        $options = ['include_bom' => true];

        try {
            $this->adapter->export($tempFile, 'csv', $exportData, $options);

            $content = file_get_contents($tempFile);
            // Check for UTF-8 BOM
            $this->assertStringStartsWith("\xEF\xBB\xBF", $content);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithMixedDataTypes(): void
    {
        $exportData = $this->createMixedTypeExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        try {
            $this->adapter->export($tempFile, 'csv', $exportData);

            $content = file_get_contents($tempFile);
            $this->assertStringContainsString('Sample Text', $content);
            $this->assertStringContainsString('123.45', $content);
            $this->assertStringContainsString('2024-01-15', $content);
            $this->assertStringContainsString('2024-01-15 14:30:00', $content);
            $this->assertStringContainsString('1', $content); // Boolean true

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithNullValues(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));

        $record = new ExportRecord();
        $record->addValue('id', 1, ExportValue::TYPE_INTEGER);
        $record->addValue('name', null, ExportValue::TYPE_STRING);

        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record);

        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        try {
            $this->adapter->export($tempFile, 'csv', $exportData);

            $content = file_get_contents($tempFile);
            $lines = explode("\n", trim($content));
            $this->assertCount(2, $lines); // Header + 1 data row
            $this->assertStringContainsString('1,', $content); // Null becomes empty string

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithFilePointer(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        try {
            $filePointer = fopen($tempFile, 'w');
            $this->adapter->export($filePointer, 'csv', $exportData);
            fclose($filePointer);

            $this->assertFileExists($tempFile);
            $content = file_get_contents($tempFile);
            $this->assertStringContainsString('ID,Name,Email', $content);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithInvalidFilePointer(): void
    {
        $exportData = $this->createTestExportData();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('File parameter must be either a string');

        $this->adapter->export(123, 'csv', $exportData);
    }

    public function testExportWithUnwritableFile(): void
    {
        $exportData = $this->createTestExportData();
        $invalidPath = '/invalid/path/file.csv';

        $this->expectException(\Exception::class);
        $this->expectExceptionMessageMatches('/Cannot open file for writing|Directory does not exist/');

        $this->adapter->export($invalidPath, 'csv', $exportData);
    }

    public function testExportWithConfiguration(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        // Set configuration
        $this->adapter->setConfiguration([
            'delimiter' => 'semicolon',
            'enclosure' => "'",
            'include_bom' => true
        ]);

        try {
            $this->adapter->export($tempFile, 'csv', $exportData);

            $content = file_get_contents($tempFile);
            $this->assertStringStartsWith("\xEF\xBB\xBF", $content); // BOM
            $this->assertStringContainsString("ID;Name;Email", $content); // Semicolon delimiter
            $this->assertStringContainsString("'John Doe'", $content); // Single quote enclosure

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportLogsProgress(): void
    {
        // Create large dataset to trigger progress logging
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));

        $exportData = new ExportData('TestModel', $header);
        for ($i = 1; $i <= 5001; $i++) {
            $record = new ExportRecord();
            $record->addValue('id', $i, ExportValue::TYPE_INTEGER);
            $exportData->addRecord($record);
        }

        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        try {
            $this->adapter->export($tempFile, 'csv', $exportData);

            // Check that progress was logged
            $this->assertNotEmpty($this->logger->infos);
            $this->assertStringContainsString('5000 records processed', $this->logger->infos[0]);
            $this->assertStringContainsString('CSV export completed: 5001 records processed', end($this->logger->infos));

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    // Test date formatting functionality

    public function testDateFormatSettersAndGetters(): void
    {
        // Test default formats
        $this->assertEquals('Y-m-d', $this->adapter->getDateFormat());
        $this->assertEquals('Y-m-d H:i:s', $this->adapter->getDatetimeFormat());

        // Test setters
        $this->adapter->setDateFormat('d.m.Y');
        $this->adapter->setDatetimeFormat('d.m.Y H:i');

        $this->assertEquals('d.m.Y', $this->adapter->getDateFormat());
        $this->assertEquals('d.m.Y H:i', $this->adapter->getDatetimeFormat());
    }

    public function testExportWithCustomDateFormats(): void
    {
        $exportData = $this->createDateExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        $options = [
            'date_format' => 'd.m.Y',
            'datetime_format' => 'd.m.Y H:i'
        ];

        try {
            $this->adapter->export($tempFile, 'csv', $exportData, $options);

            $content = file_get_contents($tempFile);
            $this->assertStringContainsString('15.01.2024', $content); // Date format
            $this->assertStringContainsString('15.01.2024 14:30', $content); // DateTime format

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithUSDateFormats(): void
    {
        $exportData = $this->createDateExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        $options = [
            'date_format' => 'm/d/Y',
            'datetime_format' => 'm/d/Y H:i'
        ];

        try {
            $this->adapter->export($tempFile, 'csv', $exportData, $options);

            $content = file_get_contents($tempFile);
            $this->assertStringContainsString('01/15/2024', $content); // US Date format
            $this->assertStringContainsString('01/15/2024 14:30', $content); // US DateTime format

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithDateFormatsFromConfiguration(): void
    {
        $exportData = $this->createDateExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        // Set configuration
        $this->adapter->setConfiguration([
            'date_format' => 'd-m-Y',
            'datetime_format' => 'd-m-Y H:i:s'
        ]);

        try {
            $this->adapter->export($tempFile, 'csv', $exportData);

            $content = file_get_contents($tempFile);
            $this->assertStringContainsString('15-01-2024', $content); // Date format from config
            $this->assertStringContainsString('15-01-2024 14:30:00', $content); // DateTime format from config

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportValueFormatTakesPrecedence(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('date_col', 'Date Column', ExportValue::TYPE_DATE));
        $header->addColumn(new ExportColumn('datetime_col', 'DateTime Column', ExportValue::TYPE_DATETIME));

        $record = new ExportRecord();
        // Add values with custom formats
        $dateValue = new ExportValue('2024-01-15', ExportValue::TYPE_DATE, 'j F Y'); // Long format
        $datetimeValue = new ExportValue('2024-01-15 14:30:00', ExportValue::TYPE_DATETIME, 'j F Y, H:i'); // Long format
        $record->addValue('date_col', $dateValue);
        $record->addValue('datetime_col', $datetimeValue);

        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record);

        $tempFile = tempnam(sys_get_temp_dir(), 'csv_test_') . '.csv';

        // Set different adapter formats
        $options = [
            'date_format' => 'd.m.Y',
            'datetime_format' => 'd.m.Y H:i'
        ];

        try {
            $this->adapter->export($tempFile, 'csv', $exportData, $options);

            $content = file_get_contents($tempFile);
            // Should use ExportValue format, not adapter format
            $this->assertStringContainsString('15 January 2024', $content); // ExportValue format
            $this->assertStringContainsString('15 January 2024, 14:30', $content); // ExportValue format
            // Should NOT contain adapter format
            $this->assertStringNotContainsString('15.01.2024', $content);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    // Test private methods via reflection

    public function testNormalizeDelimiter(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('normalizeDelimiter');
        $method->setAccessible(true);

        $this->assertEquals(',', $method->invoke($this->adapter, 'comma'));
        $this->assertEquals(';', $method->invoke($this->adapter, 'semicolon'));
        $this->assertEquals("\t", $method->invoke($this->adapter, 'tab'));
        $this->assertEquals('|', $method->invoke($this->adapter, 'pipe'));
        $this->assertEquals(',', $method->invoke($this->adapter, 'unknown')); // Default
    }

    public function testFormatValueForCsv(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('formatValueForCsv');
        $method->setAccessible(true);

        // Test different value types
        $stringValue = new ExportValue('text', ExportValue::TYPE_STRING);
        $this->assertEquals('text', $method->invoke($this->adapter, $stringValue));

        $intValue = new ExportValue(123, ExportValue::TYPE_INTEGER);
        $this->assertEquals('123', $method->invoke($this->adapter, $intValue));

        $floatValue = new ExportValue(123.45, ExportValue::TYPE_FLOAT);
        $this->assertEquals('123.45', $method->invoke($this->adapter, $floatValue));

        $boolTrueValue = new ExportValue(true, ExportValue::TYPE_BOOLEAN);
        $this->assertEquals('1', $method->invoke($this->adapter, $boolTrueValue));

        $boolFalseValue = new ExportValue(false, ExportValue::TYPE_BOOLEAN);
        $this->assertEquals('0', $method->invoke($this->adapter, $boolFalseValue));

        $nullValue = new ExportValue(null, ExportValue::TYPE_STRING);
        $this->assertEquals('', $method->invoke($this->adapter, $nullValue));
    }

    public function testFormatDateValue(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('formatDateValue');
        $method->setAccessible(true);

        // Test DateTime object with default formats
        $dateTime = new \DateTime('2024-01-15 14:30:00');
        $this->assertEquals('2024-01-15', $method->invoke($this->adapter, $dateTime, ExportValue::TYPE_DATE));
        $this->assertEquals('2024-01-15 14:30:00', $method->invoke($this->adapter, $dateTime, ExportValue::TYPE_DATETIME));

        // Test string date with default formats
        $this->assertEquals('2024-01-15', $method->invoke($this->adapter, '2024-01-15', ExportValue::TYPE_DATE));
        $this->assertEquals('2024-01-15 14:30:00', $method->invoke($this->adapter, '2024-01-15 14:30:00', ExportValue::TYPE_DATETIME));

        // Test with custom format (highest priority)
        $this->assertEquals('15.01.2024', $method->invoke($this->adapter, $dateTime, ExportValue::TYPE_DATE, 'd.m.Y'));
        $this->assertEquals('15.01.2024 14:30', $method->invoke($this->adapter, $dateTime, ExportValue::TYPE_DATETIME, 'd.m.Y H:i'));

        // Test with adapter format change
        $this->adapter->setDateFormat('d/m/Y');
        $this->adapter->setDatetimeFormat('d/m/Y H:i');
        $this->assertEquals('15/01/2024', $method->invoke($this->adapter, $dateTime, ExportValue::TYPE_DATE));
        $this->assertEquals('15/01/2024 14:30', $method->invoke($this->adapter, $dateTime, ExportValue::TYPE_DATETIME));

        // Test custom format still takes precedence
        $this->assertEquals('15-01-2024', $method->invoke($this->adapter, $dateTime, ExportValue::TYPE_DATE, 'd-m-Y'));

        // Test invalid date
        $this->assertEquals('invalid', $method->invoke($this->adapter, 'invalid', ExportValue::TYPE_DATE));
    }

    public function testGetDecimalPlaces(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('getDecimalPlaces');
        $method->setAccessible(true);

        $this->assertEquals(2, $method->invoke($this->adapter, '0.00'));
        $this->assertEquals(3, $method->invoke($this->adapter, '0.000'));
        $this->assertEquals(1, $method->invoke($this->adapter, '#,##0.0'));
        $this->assertEquals(4, $method->invoke($this->adapter, '0.1234'));
        $this->assertEquals(2, $method->invoke($this->adapter, 'no_decimal')); // Default

        // Test the specific case that was failing
        $result = $method->invoke($this->adapter, '0.00');
        $this->assertEquals(2, $result, "Expected 2 decimal places for '0.00', got: " . $result);
    }

    // Helper methods

    private function createTestExportData(): ExportData
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('email', 'Email', ExportValue::TYPE_STRING));

        $record1 = new ExportRecord();
        $record1->addValue('id', 1, ExportValue::TYPE_INTEGER);
        $record1->addValue('name', 'John Doe', ExportValue::TYPE_STRING);
        $record1->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);

        $record2 = new ExportRecord();
        $record2->addValue('id', 2, ExportValue::TYPE_INTEGER);
        $record2->addValue('name', 'Jane Smith', ExportValue::TYPE_STRING);
        $record2->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);

        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record1);
        $exportData->addRecord($record2);

        return $exportData;
    }

    protected function createMixedTypeExportData(): ExportData
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('text_col', 'Text Column', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('number_col', 'Number Column', ExportValue::TYPE_FLOAT));
        $header->addColumn(new ExportColumn('date_col', 'Date Column', ExportValue::TYPE_DATE));
        $header->addColumn(new ExportColumn('datetime_col', 'DateTime Column', ExportValue::TYPE_DATETIME));
        $header->addColumn(new ExportColumn('bool_col', 'Boolean Column', ExportValue::TYPE_BOOLEAN));

        $record = new ExportRecord();
        $record->addValue('text_col', 'Sample Text', ExportValue::TYPE_STRING);
        $record->addValue('number_col', 123.45, ExportValue::TYPE_FLOAT);
        $record->addValue('date_col', '2024-01-15', ExportValue::TYPE_DATE);
        $record->addValue('datetime_col', '2024-01-15 14:30:00', ExportValue::TYPE_DATETIME);
        $record->addValue('bool_col', true, ExportValue::TYPE_BOOLEAN);

        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record);

        return $exportData;
    }

    private function createDateExportData(): ExportData
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('date_col', 'Date Column', ExportValue::TYPE_DATE));
        $header->addColumn(new ExportColumn('datetime_col', 'DateTime Column', ExportValue::TYPE_DATETIME));

        $record = new ExportRecord();
        $record->addValue('date_col', '2024-01-15', ExportValue::TYPE_DATE);
        $record->addValue('datetime_col', '2024-01-15 14:30:00', ExportValue::TYPE_DATETIME);

        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record);

        return $exportData;
    }
}
