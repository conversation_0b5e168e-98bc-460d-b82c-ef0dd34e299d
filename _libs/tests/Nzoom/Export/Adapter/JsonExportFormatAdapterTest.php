<?php

namespace Tests\Nzoom\Export\Adapter;

use Tests\Nzoom\Export\ExportTestCase;
use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Adapter\JsonExportFormatAdapter;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\I18n\I18n;

/**
 * Mock logger for testing
 */
class MockJsonLogger
{
    public $errors = [];
    public $infos = [];

    public function error($message)
    {
        $this->errors[] = $message;
    }

    public function info($message)
    {
        $this->infos[] = $message;
    }

    public function warning($message)
    {
        // For compatibility
    }
}

/**
 * Test case for JsonExportFormatAdapter
 *
 * Focuses on JSON-specific functionality, avoiding duplication with AbstractExportFormatAdapterTest
 */
class JsonExportFormatAdapterTest extends ExportTestCase
{
    private JsonExportFormatAdapter $adapter;
    private RegistryMock $registry;
    private $translator;
    private MockJsonLogger $logger;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../../TestHelpers/GlobalMocks.php';

        // Create mock dependencies
        $this->translator = $this->createMock(I18n::class);
        $this->translator->method('translate')->willReturnCallback(function($key, $params = []) {
            return $key; // Simple pass-through for testing
        });

        $this->logger = new MockJsonLogger();

        // Create registry mock
        $this->registry = new RegistryMock();
        $this->registry->set('translater', $this->translator);
        $this->registry->set('logger', $this->logger);

        // Create adapter instance
        $this->adapter = new JsonExportFormatAdapter($this->registry, 'test_module', 'test_controller');
    }

    // Test JSON-specific interface methods

    public function testGetSupportedExtensions(): void
    {
        $extensions = JsonExportFormatAdapter::getSupportedExtensions();
        $this->assertIsArray($extensions);
        $this->assertContains('json', $extensions);
        $this->assertCount(1, $extensions);
    }

    public function testSupportsFormat(): void
    {
        $this->assertTrue(JsonExportFormatAdapter::supportsFormat('json'));
        $this->assertTrue(JsonExportFormatAdapter::supportsFormat('JSON')); // Case insensitive
        $this->assertFalse(JsonExportFormatAdapter::supportsFormat('csv'));
        $this->assertFalse(JsonExportFormatAdapter::supportsFormat('xlsx'));
        $this->assertFalse(JsonExportFormatAdapter::supportsFormat(''));
    }

    public function testGetMimeType(): void
    {
        $this->assertEquals('application/json', $this->adapter->getMimeType());
        $this->assertEquals('application/json', $this->adapter->getMimeType('json'));
        $this->assertEquals('application/json', $this->adapter->getMimeType('unsupported'));
    }

    public function testGetDefaultExtension(): void
    {
        $this->assertEquals('json', $this->adapter->getDefaultExtension());
    }

    public function testGetFormatName(): void
    {
        $this->assertEquals('json', $this->adapter->getFormatName());
    }

    public function testGetFormatOptions(): void
    {
        $options = $this->adapter->getFormatOptions();

        $this->assertIsArray($options);
        $this->assertArrayHasKey('output_structure', $options);
        $this->assertArrayHasKey('pretty_print', $options);
        $this->assertArrayHasKey('include_metadata', $options);
        $this->assertArrayHasKey('unescaped_unicode', $options);
        $this->assertArrayHasKey('unescaped_slashes', $options);
        $this->assertArrayHasKey('numeric_check', $options);

        // Test output structure options
        $structureOption = $options['output_structure'];
        $this->assertEquals('select', $structureOption['type']);
        $this->assertEquals('Output Structure', $structureOption['label']);
        $this->assertArrayHasKey('array', $structureOption['options']);
        $this->assertArrayHasKey('object', $structureOption['options']);
        $this->assertArrayHasKey('nested', $structureOption['options']);
        $this->assertEquals('array', $structureOption['default']);

        // Test pretty print options
        $prettyOption = $options['pretty_print'];
        $this->assertEquals('checkbox', $prettyOption['type']);
        $this->assertEquals('Pretty Print', $prettyOption['label']);
        $this->assertTrue($prettyOption['default']);

        // Test metadata options
        $metadataOption = $options['include_metadata'];
        $this->assertEquals('checkbox', $metadataOption['type']);
        $this->assertEquals('Include Metadata', $metadataOption['label']);
        $this->assertFalse($metadataOption['default']);
    }

    // Test JSON-specific export functionality

    public function testExportWithUnsupportedType(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unsupported export type: csv');

        $this->adapter->export($tempFile, 'csv', $exportData);

        // Clean up
        if (file_exists($tempFile)) {
            unlink($tempFile);
        }
    }

    public function testExportToTempFile(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        try {
            $this->adapter->export($tempFile, 'json', $exportData);

            // Verify file was created
            $this->assertFileExists($tempFile);
            $this->assertGreaterThan(0, filesize($tempFile));

            // Verify JSON content
            $content = file_get_contents($tempFile);
            $data = json_decode($content, true);
            
            $this->assertIsArray($data);
            $this->assertCount(2, $data); // 2 records
            $this->assertArrayHasKey('id', $data[0]);
            $this->assertArrayHasKey('name', $data[0]);
            $this->assertArrayHasKey('email', $data[0]);
            $this->assertEquals(1, $data[0]['id']);
            $this->assertEquals('John Doe', $data[0]['name']);
            $this->assertEquals('<EMAIL>', $data[0]['email']);

        } finally {
            // Clean up
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportToPhpOutput(): void
    {
        $exportData = $this->createTestExportData();

        // Capture output
        ob_start();

        try {
            $this->adapter->export('php://output', 'json', $exportData);
            $output = ob_get_contents();

            // Should have JSON content
            $this->assertNotEmpty($output);
            $data = json_decode($output, true);
            $this->assertIsArray($data);
            $this->assertCount(2, $data);

        } finally {
            ob_end_clean();
        }
    }

    public function testExportWithObjectStructure(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        $options = ['output_structure' => 'object'];

        try {
            $this->adapter->export($tempFile, 'json', $exportData, $options);

            $content = file_get_contents($tempFile);
            $data = json_decode($content, true);
            
            $this->assertIsArray($data);
            $this->assertArrayHasKey('data', $data);
            $this->assertIsArray($data['data']);
            $this->assertCount(2, $data['data']);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithNestedStructure(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        $options = ['output_structure' => 'nested'];

        try {
            $this->adapter->export($tempFile, 'json', $exportData, $options);

            $content = file_get_contents($tempFile);
            $data = json_decode($content, true);
            
            $this->assertIsArray($data);
            $this->assertArrayHasKey('id', $data);
            $this->assertArrayHasKey('name', $data);
            $this->assertArrayHasKey('email', $data);
            $this->assertIsArray($data['id']);
            $this->assertCount(2, $data['id']); // 2 records
            $this->assertEquals([1, 2], $data['id']);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithMetadata(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        $options = ['include_metadata' => true];

        try {
            $this->adapter->export($tempFile, 'json', $exportData, $options);

            $content = file_get_contents($tempFile);
            $data = json_decode($content, true);
            
            $this->assertIsArray($data);
            $this->assertArrayHasKey('data', $data);
            $this->assertArrayHasKey('metadata', $data);
            
            $metadata = $data['metadata'];
            $this->assertArrayHasKey('export_date', $metadata);
            $this->assertArrayHasKey('record_count', $metadata);
            $this->assertArrayHasKey('columns', $metadata);
            $this->assertArrayHasKey('format', $metadata);
            $this->assertArrayHasKey('structure', $metadata);
            
            $this->assertEquals('json', $metadata['format']);
            $this->assertEquals('array', $metadata['structure']);
            $this->assertIsArray($metadata['columns']);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithMixedDataTypes(): void
    {
        $exportData = $this->createMixedTypeExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        try {
            $this->adapter->export($tempFile, 'json', $exportData);

            $content = file_get_contents($tempFile);
            $data = json_decode($content, true);

            $this->assertIsArray($data);
            $this->assertCount(1, $data);

            $record = $data[0];
            $this->assertEquals('Sample Text', $record['text_col']);
            $this->assertEquals(123.45, $record['number_col']);
            $this->assertEquals('2024-01-15', $record['date_col']);
            $this->assertStringContainsString('2024-01-15', $record['datetime_col']); // ISO 8601 format
            $this->assertTrue($record['bool_col']);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithNullValues(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));

        $record = new ExportRecord();
        $record->addValue('id', 1, ExportValue::TYPE_INTEGER);
        $record->addValue('name', null, ExportValue::TYPE_STRING);

        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record);

        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        try {
            $this->adapter->export($tempFile, 'json', $exportData);

            $content = file_get_contents($tempFile);
            $data = json_decode($content, true);

            $this->assertIsArray($data);
            $this->assertCount(1, $data);
            $this->assertEquals(1, $data[0]['id']);
            $this->assertNull($data[0]['name']);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithFilePointer(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        try {
            $filePointer = fopen($tempFile, 'w');
            $this->adapter->export($filePointer, 'json', $exportData);
            fclose($filePointer);

            $this->assertFileExists($tempFile);
            $content = file_get_contents($tempFile);
            $data = json_decode($content, true);
            $this->assertIsArray($data);
            $this->assertCount(2, $data);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithInvalidFilePointer(): void
    {
        $exportData = $this->createTestExportData();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('File parameter must be either a string');

        $this->adapter->export(123, 'json', $exportData);
    }

    public function testExportWithUnwritableFile(): void
    {
        $exportData = $this->createTestExportData();
        $invalidPath = '/invalid/path/file.json';

        $this->expectException(\Exception::class);
        $this->expectExceptionMessageMatches('/Cannot write to file|Directory does not exist/');

        $this->adapter->export($invalidPath, 'json', $exportData);
    }

    public function testExportWithConfiguration(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        // Set configuration
        $this->adapter->setConfiguration([
            'output_structure' => 'object',
            'include_metadata' => true,
            'pretty_print' => false
        ]);

        try {
            $this->adapter->export($tempFile, 'json', $exportData);

            $content = file_get_contents($tempFile);
            $data = json_decode($content, true);

            $this->assertIsArray($data);
            $this->assertArrayHasKey('data', $data);
            $this->assertArrayHasKey('metadata', $data);

            // Should not be pretty printed (no extra whitespace)
            $this->assertStringNotContainsString('    ', $content); // No indentation

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithJsonOptions(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        $options = [
            'pretty_print' => false,
            'unescaped_unicode' => false,
            'unescaped_slashes' => false,
            'numeric_check' => true
        ];

        try {
            $this->adapter->export($tempFile, 'json', $exportData, $options);

            $content = file_get_contents($tempFile);
            $this->assertNotEmpty($content);

            // Should not be pretty printed
            $this->assertStringNotContainsString('    ', $content);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportLogsCompletion(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        try {
            $this->adapter->export($tempFile, 'json', $exportData);

            // Check that completion was logged
            $this->assertNotEmpty($this->logger->infos);
            $this->assertStringContainsString('JSON export completed', $this->logger->infos[0]);

        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithInvalidJson(): void
    {
        // Create data that might cause JSON encoding issues
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('data', 'Data', ExportValue::TYPE_STRING));

        $record = new ExportRecord();
        // Add invalid UTF-8 sequence that might cause json_encode to fail
        $record->addValue('data', "\xB1\x31", ExportValue::TYPE_STRING);

        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record);

        $tempFile = tempnam(sys_get_temp_dir(), 'json_test_') . '.json';

        try {
            // This should handle the JSON encoding gracefully
            $this->adapter->export($tempFile, 'json', $exportData);

            // If we get here, the export succeeded despite potential encoding issues
            $this->assertFileExists($tempFile);

        } catch (\Exception $e) {
            // If JSON encoding fails, we should get a proper error message
            $this->assertStringContainsString('Failed to encode data to JSON', $e->getMessage());
        } finally {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    // Test private methods via reflection

    public function testFormatValueForJson(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('formatValueForJson');
        $method->setAccessible(true);

        // Test different value types
        $stringValue = new ExportValue('text', ExportValue::TYPE_STRING);
        $this->assertEquals('text', $method->invoke($this->adapter, $stringValue));

        $intValue = new ExportValue(123, ExportValue::TYPE_INTEGER);
        $this->assertEquals(123, $method->invoke($this->adapter, $intValue));

        $floatValue = new ExportValue(123.45, ExportValue::TYPE_FLOAT);
        $this->assertEquals(123.45, $method->invoke($this->adapter, $floatValue));

        $boolTrueValue = new ExportValue(true, ExportValue::TYPE_BOOLEAN);
        $this->assertTrue($method->invoke($this->adapter, $boolTrueValue));

        $boolFalseValue = new ExportValue(false, ExportValue::TYPE_BOOLEAN);
        $this->assertFalse($method->invoke($this->adapter, $boolFalseValue));

        $nullValue = new ExportValue(null, ExportValue::TYPE_STRING);
        $this->assertNull($method->invoke($this->adapter, $nullValue));
    }

    public function testFormatDateValue(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('formatDateValue');
        $method->setAccessible(true);

        // Test DateTime object
        $dateTime = new \DateTime('2024-01-15 14:30:00');
        $this->assertEquals('2024-01-15', $method->invoke($this->adapter, $dateTime, ExportValue::TYPE_DATE));
        $this->assertStringContainsString('2024-01-15T14:30:00', $method->invoke($this->adapter, $dateTime, ExportValue::TYPE_DATETIME));

        // Test string date
        $this->assertEquals('2024-01-15', $method->invoke($this->adapter, '2024-01-15', ExportValue::TYPE_DATE));
        $this->assertStringContainsString('2024-01-15', $method->invoke($this->adapter, '2024-01-15 14:30:00', ExportValue::TYPE_DATETIME));

        // Test invalid date
        $this->assertEquals('invalid', $method->invoke($this->adapter, 'invalid', ExportValue::TYPE_DATE));
    }

    public function testGetJsonOptions(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('getJsonOptions');
        $method->setAccessible(true);

        // Test default options
        $options = $method->invoke($this->adapter, []);
        $this->assertIsInt($options);
        $this->assertTrue(($options & JSON_PRETTY_PRINT) !== 0); // Pretty print enabled by default

        // Test custom options
        $customOptions = [
            'pretty_print' => false,
            'unescaped_unicode' => true,
            'unescaped_slashes' => true,
            'numeric_check' => true
        ];

        $options = $method->invoke($this->adapter, $customOptions);
        $this->assertIsInt($options);
        $this->assertTrue(($options & JSON_PRETTY_PRINT) === 0); // Pretty print disabled
        $this->assertTrue(($options & JSON_UNESCAPED_UNICODE) !== 0); // Unicode enabled
        $this->assertTrue(($options & JSON_NUMERIC_CHECK) !== 0); // Numeric check enabled
    }

    // Helper methods

    private function createTestExportData(): ExportData
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('email', 'Email', ExportValue::TYPE_STRING));

        $record1 = new ExportRecord();
        $record1->addValue('id', 1, ExportValue::TYPE_INTEGER);
        $record1->addValue('name', 'John Doe', ExportValue::TYPE_STRING);
        $record1->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);

        $record2 = new ExportRecord();
        $record2->addValue('id', 2, ExportValue::TYPE_INTEGER);
        $record2->addValue('name', 'Jane Smith', ExportValue::TYPE_STRING);
        $record2->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);

        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record1);
        $exportData->addRecord($record2);

        return $exportData;
    }

    protected function createMixedTypeExportData(): ExportData
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('text_col', 'Text Column', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('number_col', 'Number Column', ExportValue::TYPE_FLOAT));
        $header->addColumn(new ExportColumn('date_col', 'Date Column', ExportValue::TYPE_DATE));
        $header->addColumn(new ExportColumn('datetime_col', 'DateTime Column', ExportValue::TYPE_DATETIME));
        $header->addColumn(new ExportColumn('bool_col', 'Boolean Column', ExportValue::TYPE_BOOLEAN));

        $record = new ExportRecord();
        $record->addValue('text_col', 'Sample Text', ExportValue::TYPE_STRING);
        $record->addValue('number_col', 123.45, ExportValue::TYPE_FLOAT);
        $record->addValue('date_col', '2024-01-15', ExportValue::TYPE_DATE);
        $record->addValue('datetime_col', '2024-01-15 14:30:00', ExportValue::TYPE_DATETIME);
        $record->addValue('bool_col', true, ExportValue::TYPE_BOOLEAN);

        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record);

        return $exportData;
    }
}
